import { dictMaps } from './entities';

// 表格 Tag 颜色
export const tagColors: dictMaps[] = [{
  label: '功能',
  key: '1',
  options: [{
    label: '默认',
    value: 'default'
  }, {
    label: '成功',
    value: 'success'
  }, {
    label: '信息',
    value: 'processing'
  }, {
    label: '警告',
    value: 'warning'
  }, {
    label: '危险',
    value: 'error'
  }]
}, {
  label: '颜色',
  key: '2',
  options: [{
    label: '粉色',
    value: 'pink'
  }, {
    label: '品红',
    value: 'magenta'
  }, {
    label: '红色',
    value: 'red'
  }, {
    label: '火山红',
    value: 'volcano'
  }, {
    label: '橙色',
    value: 'orange'
  }, {
    label: '黄色',
    value: 'yellow'
  }, {
    label: '金色',
    value: 'gold'
  }, {
    label: '青色',
    value: 'cyan'
  }, {
    label: '青橙绿',
    value: 'lime'
  }, {
    label: '绿色',
    value: 'green'
  }, {
    label: '蓝色',
    value: 'blue'
  }, {
    label: '极客蓝',
    value: 'geekblue'
  }, {
    label: '紫色',
    value: 'purple'
  }]
}];

// 文本样式
export const stylesType: string[] = ['secondary', 'success', 'warning', 'danger'];
export const textStyles : dictMaps[] = [{
  label: '颜色',
  key: '1',
  options: [{
    label: '次级',
    value: 'secondary'
  }, {
    label: '成功',
    value: 'success'
  }, {
    label: '警告',
    value: 'warning'
  }, {
    label: '危险',
    value: 'danger'
  }]
}, {
  label: '样式',
  key: '2',
  options: [{
    label: '禁用',
    value: 'disabled'
  }, {
    label: '标记',
    value: 'mark'
  }, {
    label: '代码块',
    value: 'code'
  }, {
    label: '快捷键',
    value: 'keyboard'
  }, {
    label: '下划线',
    value: 'underline'
  }, {
    label: '删除线',
    value: 'delete'
  }, {
    label: '粗体',
    value: 'strong'
  }, {
    label: '斜体',
    value: 'italic'
  }]
}];

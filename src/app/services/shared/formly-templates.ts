import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { FieldTypeConfig } from '@ngx-formly/core';
import { FieldType } from '@ngx-formly/material/form-field';

@Component({
  selector: 'formly-field-combobox',
  template: `
    <mat-select
      #select
      [formControl]="formControl"
      [multiple]="props['multiple']"
      [placeholder]="props.placeholder!"
      [required]="props.required!"
      [compareWith]="props['compareWith']"
    >
      @for (option of getOptions(); track option) {
        <mat-option [value]="getOptionValue(option)">
          {{ getOptionLabel(option) }}
        </mat-option>
      }
    </mat-select>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, MatSelectModule],
})
export class FormlyFieldCombobox extends FieldType<FieldTypeConfig> {
  @ViewChild('select', { static: true }) select!: any;

  get bindLabel() {
    return typeof this.props['labelProp'] === 'string' ? this.props['labelProp'] : '';
  }

  get bindValue() {
    return typeof this.props['valueProp'] === 'string' ? this.props['valueProp'] : undefined;
  }

  getOptionLabel(option: any): string {
    const labelProp = this.bindLabel;
    return labelProp ? option[labelProp] : option;
  }

  getOptionValue(option: any): any {
    const valueProp = this.bindValue;
    return valueProp ? option[valueProp] : option;
  }

  getOptions(): any[] {
    return Array.isArray(this.props.options) ? this.props.options : [];
  }

  // The original `onContainerClick` has been covered up in FieldType, so we should redefine it.
  override onContainerClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (/mat-form-field|mat-select/g.test(target.parentElement?.classList[0] || '')) {
      this.select.focus();
      this.select.open();
    }
  }
}

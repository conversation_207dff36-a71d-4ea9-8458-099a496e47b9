@for (group of icons | keyvalue; track trackByIcon($index, group)) {
  <mat-card>
    <mat-card-header class="text-capitalize m-b-16 text-gray-500">{{ group.key }}</mat-card-header>
    <mat-card-content>
      <div fxLayout="row wrap" fxLayoutGap="16px grid">
        @for (icon of group.value; track icon) {
          <div
            fxFlex.gt-md="11.11"
            fxFlex.md="20"
            fxFlex.sm="25"
            fxFlex.xs="33.33"
            >
            <div class="text-center">
              <button mat-icon-button (click)="iconClick(icon)">
                <mat-icon fontSet="material-symbols-rounded" class="icon-36">{{ icon }}</mat-icon>
              </button>
              <p class="f-s-12">{{ icon }}</p>
            </div>
          </div>
        }
      </div>
    </mat-card-content>
  </mat-card>
}

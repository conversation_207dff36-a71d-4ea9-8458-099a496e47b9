import { Component, OnInit, inject } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-dashboard',
  templateUrl: './index.html',
  styleUrls: ['./index.scss'],
  imports: [TranslateModule, MatButtonModule],
})
export class Dashboard implements OnInit {
  private readonly translate = inject(TranslateService);

  ngOnInit(): void {
    console.log('Dashboard component initialized');
    console.log('Current language:', this.translate.getCurrentLang());
    console.log('Default language:', this.translate.getFallbackLang());

    // Test translation
    this.translate.get('user.profile').subscribe(translation => {
      console.log('Translation test - user.profile:', translation);
    });
  }

  switchLanguage(lang: string) {
    console.log('Switching to language:', lang);
    this.translate.use(lang).subscribe(() => {
      console.log('Language switched to:', lang);
    });
  }

}

import { Component } from '@angular/core';
import { UntypedFormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormlyForm, FormlyFormOptions, FormlyFieldConfig } from '@ngx-formly/core';

import { USER, searchForm, TableColumns } from './data';

@Component({
  selector: 'app-user',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [
    ReactiveFormsModule,
    FormlyForm,
  ],
})
export class User {
  form = new UntypedFormGroup({});
  model: any = {};
  options: FormlyFormOptions = {};

  fields: FormlyFieldConfig[] = searchForm;

}

import { FormlyFieldConfig } from '@ngx-formly/core';

export interface USER {
  loginName: string;
  userName: string;
  nickName: string;
  deptName: string;
  phone: string;
  email: string;
  sex: string;
  status: string;
  avatar: string;
  loginDate: string;
  loginIp: string;
  pwdUpdateDate: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  remark: string;
  dept: string;
  roles: string[];
}

export const searchForm: FormlyFieldConfig[] = [{
  fieldGroupClassName: 'row',
  fieldGroup: [{
    className: 'col-md-3 col-sm-6',
    key: 'loginName',
    type: 'input',
    templateOptions: {
      label: '用户名称',
      required: true,
    },
  }, {
    className: 'col-md-3 col-sm-6',
    key: 'phone',
    type: 'input',
    templateOptions: {
      label: '手机号码',
    },
  }, {
    className: 'col-md-3 col-sm-6',
    key: 'status',
    type: 'select',
    templateOptions: {
      label: '状态',
    },
  }, {
    className: 'col-md-3 col-sm-6',
    key: 'exchangeid',
    type: 'input',
    templateOptions: {
      label: '创建时间',
    },
  }],
}];

export const TableColumns = [{
  header: '登录账号',
  field: 'username',
}, {
  header: '用户昵称',
  field: 'nickname',
}, {
  header: '部门',
  field: 'deptName',
}, {
  header: '性别',
  field: 'gender',
}, {
  header: '手机号码',
  field: 'mobile',
}, {
  header: '状态',
  field: 'status',
}, {
  header: '创建时间',
  field: 'createdAt',
}];

import { Routes } from '@angular/router';

import { User } from './user';
import { Role } from './role';
import { Menu } from './menu';
import { Post } from './post';
import { Dept } from './dept';
import { DictType } from './dict-type';
import { DictData } from './dict-data';
import { Config } from './config';
import { Notice } from './notice';

export const routes: Routes = [
  { path: 'user', component: User },
  { path: 'role', component: Role },
  { path: 'menu', component: Menu },
  { path: 'dept', component: Dept },
  { path: 'post', component: Post },
  { path: 'dict', component: DictType },
  { path: 'dict/:id', component: DictData },
  { path: 'config', component: Config },
  { path: 'notice', component: Notice },
];


<button mat-button fxLayout="row" fxLayoutAlign="center center" [matMenuTriggerFor]="menu">
  <img class="avatar" [src]="user.avatar" width="24" alt="avatar" />
  <span class="m-x-8" fxHide.lt-sm>{{ user.name }}</span>
</button>

<mat-menu #menu="matMenu">
  <button mat-menu-item>
    <mat-icon fontSet="material-symbols-rounded">account_circle</mat-icon>
    <span>{{ 'user.profile' | translate }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon fontSet="material-symbols-rounded">settings</mat-icon>
    <span>{{ 'user.settings' | translate }}</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item>
    <mat-icon fontSet="material-symbols-rounded">exit_to_app</mat-icon>
    <span>{{ 'user.logout' | translate }}</span>
  </button>
</mat-menu>

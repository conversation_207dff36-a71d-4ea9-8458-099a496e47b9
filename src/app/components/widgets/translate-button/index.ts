import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatPseudoCheckbox } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';

import { TranslatePipe } from '@ngx-translate/core';

import { SettingsService } from '@/services';

@Component({
  selector: 'app-translate',
  templateUrl: './index.html',
  imports: [
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatPseudoCheckbox,
    TranslatePipe,
  ],
})
export class TranslateButton {
  private settings = inject(SettingsService);

  options = this.settings.options;

  langs = [
    { value: 'auto', name: '跟随系统' },
    { value: 'en-US', name: 'English' },
    { value: 'zh-CN', name: '简体中文' },
    { value: 'zh-TW', name: '繁體中文' },
  ];

  changeLang(lang: string) {
    this.settings.setLanguage(lang);
  }
}

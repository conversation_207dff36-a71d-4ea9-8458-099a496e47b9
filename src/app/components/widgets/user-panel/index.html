<div class="lsx-user-panel"
  fxLayout="column"
  fxLayoutAlign="center center"
  routerLink="/profile/overview"
>
  <img class="lsx-user-panel-avatar" [src]="user.avatar" alt="avatar" width="64" />
  <div class="lsx-user-panel-info" fxFlex>
    <h4>{{ user.name }}</h4>
    <h5>{{ user.email }}</h5>
  </div>
</div>

<div class="lsx-user-panel-icons" fxLayout="row" fxLayoutAlign="center center">
  <button mat-icon-button matTooltip="{{ 'profile' | translate }}">
    <mat-icon fontSet="material-symbols-rounded">account_circle</mat-icon>
  </button>
  <button mat-icon-button matTooltip="{{ 'edit_profile' | translate }}">
    <mat-icon fontSet="material-symbols-rounded">settings</mat-icon>
  </button>
  <button mat-icon-button matTooltip="{{ 'logout' | translate }}">
    <mat-icon fontSet="material-symbols-rounded">exit_to_app</mat-icon>
  </button>
</div>

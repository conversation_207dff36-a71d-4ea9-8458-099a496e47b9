@use '@angular/material' as mat;

.lsx-user-panel {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  outline: none;
  background-color: var(--user-panel-background-color);
  border-radius: 0.75rem;

  &:hover,
  &:focus {
    background-color: var(--user-panel-hover-background-color);
  }
}

// Set default width and height can avoid flashing before avatar image loaded.
.lsx-user-panel-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50rem;
  // transform-origin: 0 1.5rem;
  transition: transform mat.$private-swift-ease-out-duration mat.$private-swift-ease-out-timing-function;

  [dir='rtl'] & {
    transform-origin: 3rem 1.5rem;
  }
}

.lsx-user-panel-info {
  width: 0;
  margin-left: 0.75rem;
  opacity: 1;
  transition: opacity mat.$private-swift-ease-out-duration mat.$private-swift-ease-out-timing-function;

  [dir='rtl'] & {
    margin-right: 0.75rem;
    margin-left: 0;
  }

  h4,
  h5 {
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  h4 {
    margin-bottom: 4px;
    font-size: 1rem;
    font-weight: 500;
  }

  h5 {
    font-size: 0.75rem;
    font-weight: normal;
  }
}

.lsx-user-panel-icons {
  white-space: nowrap;

  .mat-mdc-button-base,
  .mat-mdc-button-touch-target {
    width: 32px;
    height: 32px;
  }

  .mat-mdc-icon-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    mat-icon,
    svg {
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }
}

import { Component } from '@angular/core';

import screenfull from 'screenfull';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-fullscreen',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [MatButtonModule, MatIconModule],
})
export class FullscreenButton {
  get isFullscreen() {
    return screenfull.isFullscreen;
  }

  toggleFullscreen() {
    if (screenfull.isEnabled) {
      screenfull.toggle();
    }
  }
}

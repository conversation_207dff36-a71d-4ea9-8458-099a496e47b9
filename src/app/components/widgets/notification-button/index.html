<button mat-icon-button [matMenuTriggerFor]="menu">
  <mat-icon
    matBadge="5"
    matBadgeColor="warn"
    fontSet="material-symbols-rounded"
    aria-hidden="false"
  >notifications</mat-icon>
</button>

<mat-menu #menu="matMenu">
  <mat-nav-list>
    @for (message of messages; track message) {
      <mat-list-item>
        <mat-icon matListItemIcon
          class="m-x-16"
          fontSet="material-symbols-rounded"
        >info</mat-icon>
        <a matListItemTitle href="#">{{ message }}</a>
      </mat-list-item>
    }
  </mat-nav-list>
</mat-menu>

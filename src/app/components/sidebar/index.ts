import { Component, ViewEncapsulation, input, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatToolbarModule } from '@angular/material/toolbar';

import { Sidemenu } from '@/components/sidemenu';
import { Branding } from '@/components/widgets/branding';
import { UserPanel } from '@/components/widgets/user-panel';

@Component({
  selector: 'app-sidebar',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  encapsulation: ViewEncapsulation.None,
  imports: [
    MatSlideToggleModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    Branding,
    Sidemenu,
    UserPanel,
  ],
})
export class Sidebar {
  showToggle = input(true);
  showUser = input(true);
  showHeader = input(true);
  toggleChecked = input(false);

  toggleCollapsed = output();
  closeSidenav = output();
}

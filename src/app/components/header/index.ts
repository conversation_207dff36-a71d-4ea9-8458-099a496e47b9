import { Component, ViewEncapsulation, input, output } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

import { Branding } from '@/components/widgets/branding';
import { FullscreenButton } from '@/components/widgets/fullscreen-button';
import { TranslateButton } from '@/components/widgets/translate-button';
import { UserMenu } from '@/components/widgets/user-menu';
// import { Notification } from '@/components/widgets/notification';

@Component({
  selector: 'app-header',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  host: {
    class: 'lsx-header',
  },
  encapsulation: ViewEncapsulation.None,
  imports: [
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    FlexLayoutModule,
    Branding,
    FullscreenButton,
    TranslateButton,
    UserMenu,
    // Notification,
    // UserPanel,
  ],
})
export class Header {
  showToggle = input(true);
  showBranding = input(false);

  toggleSidenav = output();
  toggleSidenavNotice = output();
}

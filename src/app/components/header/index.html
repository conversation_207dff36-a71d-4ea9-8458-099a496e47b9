<mat-toolbar>
  <!-- 菜单栏切换按钮 -->
  @if (showToggle()) {
    <button mat-icon-button (click)="toggleSidenav.emit()">
      <mat-icon fontSet="material-symbols-rounded">menu</mat-icon>
    </button>
  }
  <!-- 图标 -->
  @if (showBranding()) {
    <app-branding />
  }
  <span fxFlex></span>
  <!-- 搜索 -->
  <button mat-icon-button class="lsx-toolbar-button">
    <mat-icon fontSet="material-symbols-rounded">search</mat-icon>
  </button>
  <!-- 全屏 -->
  <app-fullscreen />
  <!-- 通知 -->
  <!-- <app-notification fxHide.lt-sm /> -->
  <!-- 语言 -->
  <app-translate />
  <!-- 用户 -->
  <app-user />
  <!-- 侧边栏 -->
  <button mat-icon-button
    class="lsx-toolbar-button"
    fxHide.lt-sm
    (click)="toggleSidenavNotice.emit()"
    >
    <mat-icon fontSet="material-symbols-rounded">list</mat-icon>
  </button>
</mat-toolbar>

import { Directive, OnD<PERSON>roy, OnInit, inject, input, model } from '@angular/core';

import { NavAccordion } from './nav-accordion';

@Directive({
  selector: '[navAccordionItem]',
  exportAs: 'navAccordionItem',
  host: {
    '[class.expanded]': 'expanded',
  },
})
export class NavAccordionItem implements OnInit, OnDestroy {
  private readonly nav = inject(NavAccordion);
  // private readonly cdr = inject(ChangeDetectorRef);

  route = input('');
  type = input<'link' | 'sub' | 'extLink' | 'extTabLink'>('link');

  expanded = model.required<boolean>();

  ngOnInit() {
    this.nav.addItem(this);
  }

  ngOnDestroy() {
    this.nav.removeItem(this);
  }

  toggle() {
    this.expanded.update(value => !value);
  }
}

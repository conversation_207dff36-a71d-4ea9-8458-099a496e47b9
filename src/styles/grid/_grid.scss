@use 'variables';
@use 'breakpoints';
@use 'columns';

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--gutter) * -0.5);
  margin-left: calc(var(--gutter) * -0.5);
}

.no-gutters {
  --gutter: 0;
}

@include columns.make-grid-columns();

// xsmall: 0,
// small:  600px
// medium: 960px
// large:  1280px
// xlarge: 1920px

@each $breakpoint, $infix in variables.$breakpoint-infixs {
  @if ($breakpoint== 'xsmall') {
    @include columns.loop-grid-columns(variables.$grid-columns, $infix);
  }

  @else {
    @include breakpoints.bp-gt($breakpoint) {
      @include columns.loop-grid-columns(variables.$grid-columns, $infix);
    }
  }
}
